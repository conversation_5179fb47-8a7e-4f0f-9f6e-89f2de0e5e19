import cv2
import numpy as np
import mediapipe as mp
from tensorflow.keras.models import load_model

# Load the trained model
model = load_model('sign_language_model.h5')

# MediaPipe Hands setup
mp_hands = mp.solutions.hands
hands = mp_hands.Hands(static_image_mode=False,
                       max_num_hands=1,
                       min_detection_confidence=0.5,
                       min_tracking_confidence=0.5)
mp_drawing = mp.solutions.drawing_utils

# Class names for mapping prediction indices to letters
# The labels in the dataset are 0-24, excluding 9 (J) and 25 (Z is not in the dataset)
# The LabelBinarizer in training maps these to 0-23.
class_names = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y']


def main():
    """Main function to run the prediction with hand tracking."""
    cap = cv2.VideoCapture(0)

    if not cap.isOpened():
        print("Error: Could not open camera.")
        return

    while True:
        ret, frame = cap.read()
        if not ret:
            print("Error: Failed to capture frame.")
            break

        # Flip the frame horizontally for a selfie-view display
        #frame = cv2.flip(frame, 1)
        
        # Convert the BGR image to RGB
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

        # Process the frame and find hands
        results = hands.process(rgb_frame)

        if results.multi_hand_landmarks:
            for hand_landmarks in results.multi_hand_landmarks:
                # Draw hand landmarks
                #mp_drawing.draw_landmarks(frame, hand_landmarks, mp_hands.HAND_CONNECTIONS)

                # Get bounding box around the hand
                x_coords = [landmark.x for landmark in hand_landmarks.landmark]
                y_coords = [landmark.y for landmark in hand_landmarks.landmark]

                x_min = int(min(x_coords) * frame.shape[1] - 20)
                y_min = int(min(y_coords) * frame.shape[0] - 20)
                x_max = int(max(x_coords) * frame.shape[1] + 20)
                y_max = int(max(y_coords) * frame.shape[0] + 20)
                
                # Ensure the bounding box is within the frame
                x_min = max(0, x_min)
                y_min = max(0, y_min)
                x_max = min(frame.shape[1], x_max)
                y_max = min(frame.shape[0], y_max)

                # Draw the bounding box
                cv2.rectangle(frame, (x_min, y_min), (x_max, y_max), (0, 255, 0), 2)
                
                # Extract the ROI
                roi = frame[y_min:y_max, x_min:x_max]

                if roi.size > 0:
                    # Preprocess the ROI for prediction
                    gray = cv2.cvtColor(roi, cv2.COLOR_BGR2GRAY)
                    gray = cv2.resize(gray, (28, 28), interpolation=cv2.INTER_AREA)
                    normalized = gray / 255.0
                    prediction_image = normalized.reshape(1, 28, 28, 1)

                    # Make a prediction
                    prediction = model.predict(prediction_image)
                    prediction_index = np.argmax(prediction)
                    predicted_class = class_names[prediction_index]
                    confidence = prediction[0][prediction_index]

                    # Display the prediction
                    text = f"Prediction: {predicted_class} ({confidence:.2f})"
                    cv2.putText(frame, text, (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 255), 2)

        cv2.putText(frame, "Press 'q' to quit", (10, 60),
                    cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)

        # Display the resulting frame
        cv2.imshow('Sign Language Prediction with Hand Tracking', frame)

        # Break the loop if 'q' is pressed
        if cv2.waitKey(1) & 0xFF == ord('q'):
            break

    # When everything is done, release the capture
    cap.release()
    cv2.destroyAllWindows()


if __name__ == "__main__":
    main() 